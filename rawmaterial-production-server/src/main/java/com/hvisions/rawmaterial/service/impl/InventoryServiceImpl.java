package com.hvisions.rawmaterial.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.brewage.feign.purchase.PurchaseClient;
import com.hvisions.brewage.purchase.dto.SapBaseOutputDto;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.HvExportUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.consts.InventoryState;
import com.hvisions.rawmaterial.dao.InventoryDetailMapper;
import com.hvisions.rawmaterial.dao.InventoryMapper;
import com.hvisions.rawmaterial.dao.RlManagementMapper;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusDTO;
import com.hvisions.rawmaterial.dto.storage.Rl.MaterialSiloTreeDTO;
import com.hvisions.rawmaterial.dto.storage.inventory.*;
import com.hvisions.rawmaterial.entity.TMpdInventory;
import com.hvisions.rawmaterial.entity.TMpdInventoryDetail;
import com.hvisions.rawmaterial.entity.TMpdRlManagement;
import com.hvisions.rawmaterial.service.InventoryService;
import com.hvisions.rawmaterial.service.materialsilo.MaterialSiloConfigService;
import com.hvisions.rawmaterial.service.silo.SiloRealtimeStatusService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.SerialNumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: Jcao
 * @time: 2022/4/24 16:35
 */
@Slf4j
@Service
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private RlManagementMapper rlManagementMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private InventoryDetailMapper inventoryDetailMapper;

    @Resource
    private SerialNumberUtil serialNumberUtil;

    @Resource
    private PurchaseClient purchaseClient;

    @Resource
    private MaterialSiloConfigService materialSiloConfigService;

    @Resource
    private SiloRealtimeStatusService siloRealtimeStatusService;

    /**
     * @param inventoryPageQueryDTO: 查询条件
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.storage.inventory.InventoryPageDTO>
     * @Description: 分页查询盘点任务列表
     * <AUTHOR>
     */
    /*@Override
    public Page<InventoryPageDTO> getInventoryPageList(InventoryPageQueryDTO inventoryPageQueryDTO) {
        // 处理盘点数据
        Page<InventoryPageDTO> pageList = PageHelperUtil.getPage(inventoryMapper::getInventoryPageList, inventoryPageQueryDTO, InventoryPageDTO.class);

        pageList.getContent().forEach(item -> {
            // 获取物料类型
            List<InventoryMaterialTypeDTO> materialTypes = inventoryMapper.getMaterialTypes(item.getId());
            // 获取库区信息
            List<InventoryAreaDTO> areas = inventoryMapper.getInventoryAreaInfo(item.getId());
            // 获取盘点物料详情
            List<InventoryDetailDTO> details = inventoryMapper.getInventoryDetail(item.getId());
            // 根据物料类型分类，汇总车辆已出门未收数量、已收未过账数量
            List<DailyDeliveryQtySummaryDto> vehicleQtySummarys = purchaseClient.getSummaryQtyByMaterialType().getData();

            for (InventoryMaterialTypeDTO materialType : materialTypes) {
                materialType.setSapInventory(new BigDecimal(0));
                // 获取对应物料类型下的库位信息
                List<InventoryAreaDTO> collect = areas.stream()
                        .filter(detail -> {
                            // 获取库位下的库区信息
                            if (detail.getMaterialType().equals(materialType.getId())) {
                                List<InventoryDetailDTO> collect1 = details.stream()
                                        .filter(item2 -> StringUtil.isNotEmpty(item2.getAreaId())
                                                && item2.getAreaId().equals(detail.getAreaId())
                                                && item2.getMaterialType().equals(detail.getMaterialType()))
                                        .collect(Collectors.toList());
                                detail.setDetails(collect1);
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                materialType.setAreas(collect);

                materialType.setNotPostingQty(new BigDecimal(0)); // 获取已收货未过账总数
                materialType.setNotReceiveQty(new BigDecimal(0)); // 获取已出门未收货总数
                if (vehicleQtySummarys != null && vehicleQtySummarys.size() > 0) {
                    for (DailyDeliveryQtySummaryDto vehicleQtySummary : vehicleQtySummarys) {
                        if (materialType.getId() == vehicleQtySummary.getMaterialType()){
                            materialType.setNotReceiveQty(vehicleQtySummary.getNotReceiveQty());
                            materialType.setNotPostingQty(vehicleQtySummary.getNotPostingQty());
                        }
                    }
                }

            }
            item.setMaterialTypes(materialTypes);
        });
        return pageList;
    }*/

    @Override
    public Page<InventoryPageDTO> getInventoryPageList(InventoryPageQueryDTO inventoryPageQueryDTO) {
        // 处理盘点数据
        Page<InventoryPageDTO> pageList = PageHelperUtil.getPage(inventoryMapper::getInventoryPageList, inventoryPageQueryDTO, InventoryPageDTO.class);
        return pageList;
    }


    /**
     * @param id: 盘点任务id
     * @return java.util.Map<java.lang.Integer, java.math.BigDecimal>
     * @Description: 查询SAP物料库存
     * <AUTHOR>
     */
    public Map<Integer, BigDecimal> getSapInventory(Integer id) {
        Map<Integer, BigDecimal> map = new HashMap<>();
        // 获取物料类型
        List<InventoryMaterialTypeDTO> materialTypes = inventoryMapper.getMaterialTypes(id);
        for (InventoryMaterialTypeDTO materialType : materialTypes) {
            // 获取SAP库存物料总数
            List<String> materialCodes = inventoryMapper.getMaterialCodes(materialType.getId());
            if (materialCodes != null && materialCodes.size() > 0) {
                BigDecimal total = new BigDecimal(0);
                try {
                    // 总数累加
                    ResultVO<List<SapBaseOutputDto>> resultVO = purchaseClient.inventoryQuery(materialCodes);
                    if(resultVO.getCode()!=200){
                        throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
                    }
                    List<SapBaseOutputDto> outputs = resultVO.getData();
                    for (SapBaseOutputDto output : outputs) {
                        total = total.add(new BigDecimal(output.getQty()));
                    }
                } catch (Exception e) {
                    throw new BaseKnownException(10000, "SAP接口调用异常，" + e.getMessage());
                }
                map.put(materialType.getId(), total);
            }
        }
        return map;
    }


    /**
     * @param inventoryGenerateDTO: 传参
     * @return java.lang.Integer
     * @Description: 生成盘点任务
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Integer generateInventory(InventoryGenerateDTO inventoryGenerateDTO) {
        int result = 0;
        // 根据不同的盘点类型，获取库位物料信息
        //List<RlManagementDataDTO> rlManagementDatas = rlManagementMapper.getRlManagementData(inventoryGenerateDTO);
        //查询仓库下面的所有筒仓
        List<MaterialSiloTreeDTO> materialSilos = materialSiloConfigService.getAllMaterialByCode(inventoryGenerateDTO.getCode());

        if (materialSilos == null || materialSilos.size() == 0) {
            throw new BaseKnownException(10000, "无法盘点，库内数据为空！");
        }
        // 获取所有筒仓信息的物料-去实时表取
        List<SiloRealtimeStatusDTO>  siloRealtimeStatusDTOS = siloRealtimeStatusService.getListLatestSiloRealtimeStatusBySiloNo(materialSilos.stream().map(MaterialSiloTreeDTO::getCode).collect(Collectors.toList()));
        /*if (rlManagementDatas != null && rlManagementDatas.size() > 0) {*/
            // 新增盘点任务
            TMpdInventory inventory = DtoMapper.convert(inventoryGenerateDTO, TMpdInventory.class);
            String number = serialNumberUtil.getSerialNumber("t_mpd_inventory", 3, true);
            inventory.setOrderNo("PD" + DateUtil.dateFormat(new Date(), "yyMMdd") + number);
            inventory.setTransactionTypeCode("ME21N");
            inventory.setState("0");
            inventory.setCreateTime(new Date());
            inventory.setUpdateTime(new Date());
            int res = inventoryMapper.insert(inventory);
            if (res > 0) {
                // 新增盘点任务详情
                /*for (RlManagementDataDTO rlManagementData : rlManagementDatas) {
                    TMpdInventoryDetail inventoryDetail = DtoMapper.convert(rlManagementData, TMpdInventoryDetail.class);
                    inventoryDetail.setInventoryId(inventory.getId());
                    inventoryDetail.setStorageId(rlManagementData.getId());
                    inventoryDetail.setCreatorId(inventoryGenerateDTO.getCreatorId());
                    inventoryDetail.setCreateTime(new Date());
                    inventoryDetail.setUpdaterId(inventoryGenerateDTO.getUpdaterId());
                    inventoryDetail.setUpdateTime(new Date());
                    int flag = inventoryDetailMapper.insert(inventoryDetail);
                    if (flag == 0) {
                        throw new BaseKnownException(10000, "盘点任务生成失败！");
                    }
                }*/
                for (SiloRealtimeStatusDTO siloRealtimeStatusDTO : siloRealtimeStatusDTOS){
                    TMpdInventoryDetail inventoryDetail = DtoMapper.convert(siloRealtimeStatusDTO, TMpdInventoryDetail.class);
                    inventoryDetail.setInventoryId(inventory.getId());
                    inventoryDetail.setCreatorId(inventoryGenerateDTO.getCreatorId());
                    inventoryDetail.setCreateTime(new Date());
                    inventoryDetail.setUpdaterId(inventoryGenerateDTO.getUpdaterId());
                    inventoryDetail.setUpdateTime(new Date());
                    inventoryDetail.setInventoryQuantity(siloRealtimeStatusDTO.getInventoryWeight());
                    //sap库存
                    inventoryDetail.setStockQuantity(getSapInventory(siloRealtimeStatusDTO.getMaterialCode()));

                    int flag = inventoryDetailMapper.insert(inventoryDetail);
                    if (flag == 0) {
                        throw new BaseKnownException(10000, "盘点任务生成失败！");
                    }
                }
            }
        /*}*/
        return result;
    }

    /**
     * 调用SAP接口获取实时库存
     */
    public BigDecimal getSapInventory(String materialCode) {
        BigDecimal inventory = new BigDecimal(0);
        try {
            ResultVO<List<SapBaseOutputDto>> resultVO = purchaseClient.inventoryQuery(Collections.singletonList(materialCode));
            if(resultVO.getCode()!=200){
                log.error("SAP接口调用异常，" + resultVO.getMessage());
                //throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }
            List<SapBaseOutputDto> outputs = resultVO.getData();
            for (SapBaseOutputDto output : outputs) {
                inventory = inventory.add(new BigDecimal(output.getQty()));
            }
        }catch (Exception e){
            log.error("SAP接口调用异常，" + e.getMessage());
            //throw new BaseKnownException(10000, "SAP接口调用异常，" + e.getMessage());
        }
        return inventory;
    }

    /**
     * @param id: 盘点任务id
     * @return java.lang.Integer
     * @Description: 开始盘点任务
     * <AUTHOR>
     */
    @Override
    public Integer startInventory(Integer id) {
        TMpdInventory inventory = new TMpdInventory();
        inventory.setId(id);
        inventory.setState(InventoryState.IN_EXECUTION);
        int res = inventoryMapper.updateById(inventory);
        if (res > 0) {
            // 根据盘点任务id获取详情信息
            List<TMpdInventoryDetail> inventorys = inventoryDetailMapper.selectList(new QueryWrapper<TMpdInventoryDetail>()
                    .eq("inventory_id", id));
            inventorys.forEach(item -> {
                // 库位锁定
                TMpdRlManagement rlManagement = new TMpdRlManagement();
                rlManagement.setId(item.getStorageId());
                rlManagement.setState(InventoryState.LOCKING);
                int flag = rlManagementMapper.updateById(rlManagement);
                if (flag == 0) {
                    throw new BaseKnownException(10000, "库位锁定失败！");
                }
            });
        }
        return res;
    }

    /**
     * @param inventoryFinishs: 盘点物料列表
     * @param id:               盘点任务id
     * @param checkPeople:      盘点人
     * @return java.lang.Integer
     * @Description: 完成盘点
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Integer finishInventory(List<InventoryFinishDTO> inventoryFinishs, Integer id, String checkPeople) {
        int result = 0;
        // 修改盘点任务为已完成
        TMpdInventory inventory = new TMpdInventory();
        inventory.setId(id);
        inventory.setCheckPeople(checkPeople);
        inventory.setCheckTime(new Date());
        inventory.setState(InventoryState.FINISH);
        int res = inventoryMapper.updateById(inventory);
        if (res > 0) {
            for (InventoryFinishDTO inventoryFinish : inventoryFinishs) {
                TMpdInventoryDetail inventoryDetail = DtoMapper.convert(inventoryFinish, TMpdInventoryDetail.class);
                int res2 = inventoryDetailMapper.updateById(inventoryDetail);
                if (res2 == 0) {
                    throw new BaseKnownException(10000, "完成盘点失败！");
                }
                // 库位解锁
                TMpdRlManagement rlManagement = new TMpdRlManagement();
                rlManagement.setId(inventoryFinish.getStorageId());
                rlManagement.setState(InventoryState.NOT_LOCKING);
                rlManagementMapper.updateById(rlManagement);
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Integer deleteInventory(Integer id) {
        TMpdRlManagement rlManagement = rlManagementMapper.selectById(id);
        // 删除库区数据
        if (rlManagement.getParentId().intValue() == 0) {
            List<TMpdRlManagement> rlManagements = rlManagementMapper.selectList(new QueryWrapper<TMpdRlManagement>()
                    .eq("parent_id", rlManagement.getId()));
            if (rlManagements != null && rlManagements.size() > 0) {
                rlManagements.forEach(item -> {
                    int flag = inventoryMapper.deleteById(item.getId());
                    if (flag == 0) {
                        throw new BaseKnownException(10000, "库区数据删除失败！");
                    }
                });
            }
        }
        return inventoryMapper.deleteById(id);
    }

    /**
     * @param id: 盘点计划id
     * @return com.hvisions.common.dto.ExcelExportDto
     * @Description: 导出盘点明细列表
     * <AUTHOR>
     */
    @Override
    public ExcelExportDto exportData(Integer id) {
        // 根据盘点集合id获取需求数据
        List<InventoryDetailDTO> dataList = inventoryMapper.getInventoryDetail(id);
        List<ExtendColumnInfo> extendColumnInfoList = Collections.emptyList();
        ExcelExportDto excelExportDto = HvExportUtil.exportData(dataList, InventoryDetailDTO.class, extendColumnInfoList);
        excelExportDto.setFileName("盘点明细列表");
        return excelExportDto;
    }

    /**
     * @Description: 获取盘点任务详情
     * @param id
     * @return
     */
    @Override
    public InventoryQueryDTO getInventoryDetail(Integer id) {
        InventoryQueryDTO inventory = inventoryMapper.getInventoryAndDetailById(id);
        if (inventory != null){
            //计算差异数量 = 实际盘点数量 - sap帐存数量
            inventory.getDetailList().stream().forEach(item -> {
                item.setDifferenceQuantity(item.getInventoryQuantity().subtract(item.getStockQuantity()));
            });
        }
        return inventory;
    }

    /**
     * 获取SAP帐存数量
     * @param id
     * @return
     */
    @Override
    public InventoryQueryDTO getSapInventoryList(Integer id) {
        InventoryQueryDTO inventory = inventoryMapper.getInventoryAndDetailById(id);
        if (inventory != null){
            //计算差异数量 = 实际盘点数量 - sap帐存数量
            inventory.getDetailList().stream().forEach(item -> {
                item.setStockQuantity(getSapInventory(item.getMaterialCode()));
                item.setDifferenceQuantity(item.getInventoryQuantity().subtract(item.getStockQuantity()));
            });
        }
        return inventory;
    }

    @Override
    public Integer finishInventory(InventoryQueryDTO inventoryQuery) {
        int result = 0;
        // 修改盘点任务为已完成
        TMpdInventory inventory = new TMpdInventory();
        inventory.setId(inventoryQuery.getId());
        inventory.setCheckPeople(inventoryQuery.getCheckPeople());
        inventory.setCheckTime(new Date());
        inventory.setState(InventoryState.FINISH);
        int res = inventoryMapper.updateById(inventory);
        if (res > 0) {
            for (InventoryDetailQueryDTO inventoryFinish : inventoryQuery.getDetailList()) {
                TMpdInventoryDetail inventoryDetail = DtoMapper.convert(inventoryFinish, TMpdInventoryDetail.class);
                int res2 = inventoryDetailMapper.updateById(inventoryDetail);
                if (res2 == 0) {
                    throw new BaseKnownException(10000, "完成盘点失败！");
                }
            }
        }
        return result;
    }
}
