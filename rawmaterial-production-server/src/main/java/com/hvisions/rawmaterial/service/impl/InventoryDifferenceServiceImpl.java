package com.hvisions.rawmaterial.service.impl;

import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.consts.InventoryDifferenceConstants;
import com.hvisions.rawmaterial.dao.InventoryDifferenceDetailRepository;
import com.hvisions.rawmaterial.dao.InventoryDifferenceRepository;
import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.difference.*;
import com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifference;
import com.hvisions.rawmaterial.entity.difference.TMpdInventoryDifferenceDetail;
import com.hvisions.rawmaterial.mapper.InventoryDifferenceMapper;
import com.hvisions.rawmaterial.service.InventoryDifferenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理服务实现类
 * @Date: 2024/07/14
 */
@Slf4j
@Service
public class InventoryDifferenceServiceImpl implements InventoryDifferenceService {

    @Autowired
    private InventoryDifferenceRepository inventoryDifferenceRepository;

    @Autowired
    private InventoryDifferenceDetailRepository inventoryDifferenceDetailRepository;

    @Resource
    private InventoryDifferenceMapper inventoryDifferenceMapper;

    @Autowired
    private UserClient userClient;

    @Override
    public Page<InventoryDifferenceDTO> queryInventoryDifferences(InventoryDifferenceQueryDTO queryDTO) {
        log.debug("Querying inventory differences with cache key: inventory-difference:{}", queryDTO.hashCode());
        return PageHelperUtil.getPage(inventoryDifferenceMapper::queryInventoryDifferences, queryDTO, InventoryDifferenceDTO.class);
    }

    @Override
    public InventoryDifferenceDTO getInventoryDifferenceById(Integer id) {
        log.debug("Getting inventory difference by id with cache: {}", id);
        InventoryDifferenceDTO dto = inventoryDifferenceMapper.getInventoryDifferenceById(id);
        if (dto == null) {
            throw new IllegalArgumentException("库存差异处理记录不存在");
        }
        
        // 实时查询明细列表
        Date statisticsStartDate = dto.getStatisticsStartDate();
        Date statisticsEndDate = new Date();
        //根据物料编码和统计时间范围查询各个中心的发料数量总和
        List<TMpdInventoryDifferenceDetail> detailEntities = new ArrayList<>();
        if (dto.getMaterialType().equals("稻壳")){
            detailEntities = inventoryDifferenceMapper.getRiceIssuedInventoryDifferenceCenters(
                    dto.getMaterialCode(), statisticsStartDate, statisticsEndDate);
        } else if (dto.getMaterialType().equals("高粱")) {
            detailEntities = inventoryDifferenceMapper.getSorghumIssuedInventoryDifferenceCenters(
                    dto.getMaterialCode(), statisticsStartDate, statisticsEndDate);
        }
        BigDecimal issuedQuantity = dto.getIssuedQuantity();
        //获取发料数量 计算发料数量在各个中心占有比例公式 发放量/sum(发放量)
        //List<TMpdInventoryDifferenceDetail> detailEntities = inventoryDifferenceDetailRepository.findByDifferenceId(id);
        if (CollectionUtils.isNotEmpty(detailEntities)) {
            List<InventoryDifferenceDetailDTO> detailList = detailEntities.stream()
                    .map(this::convertDetailToDTO)
                    .collect(Collectors.toList());
            dto.setDetailList(detailList);
        }
        
        return dto;
    }

    @Override
    public List<InventoryDifferenceDetailDTO> getInventoryDifferenceDetails(Integer id) {
        return null;
    }

    @Override
    @Cacheable(value = "inventory-difference-statistics", key = "'pending-differences'")
    public List<InventoryDifferenceDTO> getPendingDifferences() {
        log.debug("Getting pending differences with cache");
        return inventoryDifferenceMapper.getPendingDifferences();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateDifferenceRecords(InventoryDifferenceDTO dto) {
        log.info("开始生成库存差异处理记录");
        
        try {
            // 检查是否已存在待处理的记录
            Optional<TMpdInventoryDifference> existingOpt = inventoryDifferenceRepository
                    .findByMaterialCodeAndErpWarehouseCodeAndStatus(
                            dto.getMaterialCode(),
                            dto.getErpWarehouseCode(),
                            InventoryDifferenceConstants.STATUS_PENDING);

            if (existingOpt.isPresent()) {
                log.debug("物料{}仓库{}已存在待处理记录，跳过", dto.getMaterialCode(), dto.getErpWarehouseCode());
                throw new IllegalArgumentException("已存在待处理的记录");
            }

            // 获取最新的已处理记录，确定统计开始日期
            List<TMpdInventoryDifference> latestRecords = inventoryDifferenceRepository
                    .findLatestByMaterialAndWarehouse(
                            dto.getMaterialCode(),
                            dto.getErpWarehouseCode(),
                            PageRequest.of(0, 1));

            Date statisticsStartDate;
            BigDecimal mesInitialStock = BigDecimal.ZERO;

            if (CollectionUtils.isNotEmpty(latestRecords)) {
                TMpdInventoryDifference latestRecord = latestRecords.get(0);
                statisticsStartDate = latestRecord.getStatisticsEndDate();
                mesInitialStock = latestRecord.getMesCurrentStock();
            } else {
                // 如果没有历史记录，取用户填的开始时间
                statisticsStartDate = dto.getStatisticsStartDate();
                mesInitialStock = dto.getMesInitialStock();
            }

            // 创建新的差异处理记录
            TMpdInventoryDifference difference = createDifferenceRecord(dto, statisticsStartDate, mesInitialStock);
            inventoryDifferenceRepository.save(difference);

            log.debug("生成差异处理记录：物料{}，仓库{}，差异数量{}",
                    dto.getMaterialCode(), dto.getErpWarehouseCode(), difference.getDifferenceQuantity());
            return difference.getId();
            
        } catch (Exception e) {
            log.error("生成库存差异处理记录失败", e);
            throw new RuntimeException("生成库存差异处理记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processDifference(InventoryDifferenceProcessDTO processDTO, Integer userId) {
        log.info("开始处理库存差异，差异ID：{}", processDTO.getDifferenceId());

        try {
            // 查询差异处理记录
            Optional<TMpdInventoryDifference> differenceOpt = inventoryDifferenceRepository.findById(processDTO.getDifferenceId());
            if (!differenceOpt.isPresent()) {
                throw new IllegalArgumentException("库存差异处理记录不存在");
            }

            TMpdInventoryDifference difference = differenceOpt.get();
            if (!InventoryDifferenceConstants.STATUS_PENDING.equals(difference.getStatus())) {
                throw new IllegalArgumentException("该差异记录已处理，无法重复处理");
            }

            // 获取当前用户信息
            ResultVO<UserDTO> userResult = userClient.getUser(userId);
            UserDTO currentUser = userResult.getData();

            // 更新差异处理记录
            difference.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
            difference.setStatisticsEndDate(processDTO.getStatisticsEndDate());
            difference.setProcessTime(new Date());
            difference.setProcessRemark(processDTO.getProcessRemark());
            if (currentUser != null) {
                difference.setProcessorId(currentUser.getId());
                difference.setProcessorName(currentUser.getUserName());
            }

            inventoryDifferenceRepository.save(difference);

            // 保存处理明细
            if (CollectionUtils.isNotEmpty(processDTO.getDetailList())) {
                List<TMpdInventoryDifferenceDetail> detailEntities = processDTO.getDetailList().stream()
                        .map(detailDTO -> {
                            TMpdInventoryDifferenceDetail detail = new TMpdInventoryDifferenceDetail();
                            BeanUtils.copyProperties(detailDTO, detail);
                            detail.setDifferenceId(processDTO.getDifferenceId());
                            detail.setProcessTime(new Date());
                            return detail;
                        })
                        .collect(Collectors.toList());

                inventoryDifferenceDetailRepository.saveAll(detailEntities);
            }

            log.info("库存差异处理完成，差异ID：{}，处理人：{}", processDTO.getDifferenceId(),
                    currentUser != null ? currentUser.getUserName() : "系统");

            return true;

        } catch (Exception e) {
            log.error("处理库存差异失败，差异ID：{}", processDTO.getDifferenceId(), e);
            throw new RuntimeException("处理库存差异失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer syncSapStock(List<SapStockSyncDTO> syncDTOList) {
        log.info("开始同步SAP库存，同步数量：{}", syncDTOList.size());

        try {
            int syncCount = 0;

            for (SapStockSyncDTO syncDTO : syncDTOList) {
                // 查找对应的待处理差异记录
                Optional<TMpdInventoryDifference> differenceOpt = inventoryDifferenceRepository
                        .findByMaterialCodeAndErpWarehouseCodeAndStatus(
                                syncDTO.getMaterialCode(),
                                syncDTO.getErpWarehouseCode(),
                                InventoryDifferenceConstants.STATUS_PENDING);

                if (differenceOpt.isPresent()) {
                    TMpdInventoryDifference difference = differenceOpt.get();
                    difference.setSapStock(syncDTO.getSapStock());
                    difference.setSapSyncTime(syncDTO.getSyncTime());

                    // 重新计算差异数量
                    calculateDifferenceQuantity(difference);

                    inventoryDifferenceRepository.save(difference);
                    syncCount++;

                    log.debug("同步SAP库存：物料{}，仓库{}，库存{}",
                            syncDTO.getMaterialCode(), syncDTO.getErpWarehouseCode(), syncDTO.getSapStock());
                }
            }

            log.info("SAP库存同步完成，成功同步{}条记录", syncCount);
            return syncCount;

        } catch (Exception e) {
            log.error("同步SAP库存失败", e);
            throw new RuntimeException("同步SAP库存失败：" + e.getMessage());
        }
    }

    @Override
    public Boolean syncSingleSapStock(String materialCode, String erpWarehouseCode) {
        log.info("开始同步单个物料SAP库存，物料：{}，仓库：{}", materialCode, erpWarehouseCode);

        try {
            // 这里应该调用SAP接口获取实时库存
            // 暂时模拟返回数据
            SapStockSyncDTO syncDTO = new SapStockSyncDTO();
            syncDTO.setMaterialCode(materialCode);
            syncDTO.setErpWarehouseCode(erpWarehouseCode);
            syncDTO.setSapStock(BigDecimal.ZERO); // 实际应该从SAP获取
            syncDTO.setSyncTime(new Date());

            List<SapStockSyncDTO> syncList = Collections.singletonList(syncDTO);
            Integer syncCount = syncSapStock(syncList);

            return syncCount > 0;

        } catch (Exception e) {
            log.error("同步单个物料SAP库存失败，物料：{}，仓库：{}", materialCode, erpWarehouseCode, e);
            return false;
        }
    }

    @Override
    public InventoryDifferenceDTO calculateDifference(InventoryDifferenceDTO dto) {
        // 计算差异数量：MES当前库存-(MES期初库存+地磅收货数量-固废提报数量-发料数量)
        BigDecimal theoreticalStock = dto.getMesInitialStock()
                .add(dto.getWeighbridgeReceiptQuantity())
                .subtract(dto.getSolidWasteQuantity())
                .subtract(dto.getIssuedQuantity());

        BigDecimal differenceQuantity = dto.getMesCurrentStock().subtract(theoreticalStock);
        dto.setDifferenceQuantity(differenceQuantity);

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDifference(Integer id) {
        if (!inventoryDifferenceRepository.existsById(id)) {
            throw new IllegalArgumentException("库存差异处理记录不存在");
        }

        // 删除明细记录
        inventoryDifferenceDetailRepository.deleteByDifferenceId(id);

        // 删除主记录
        inventoryDifferenceRepository.deleteById(id);

        log.info("删除库存差异处理记录成功，ID：{}", id);
        return true;
    }

    @Override
    public InventoryDifferenceStatisticsDTO getDifferenceStatistics() {
        log.info("获取库存差异统计信息");
        
        try {
            InventoryDifferenceStatisticsDTO statistics = new InventoryDifferenceStatisticsDTO();
            
            // 获取待处理差异统计
            List<TMpdInventoryDifference> pendingDifferences = inventoryDifferenceRepository
                    .findByStatus(InventoryDifferenceConstants.STATUS_PENDING);
            
            statistics.setPendingCount(pendingDifferences.size());
            statistics.setPendingTotalDifference(pendingDifferences.stream()
                    .map(TMpdInventoryDifference::getDifferenceQuantity)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            
            // 获取已处理差异统计（最近30天）
            Calendar calendar = Calendar.getInstance();
            Date endDate = calendar.getTime();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date startDate = calendar.getTime();
            
            List<TMpdInventoryDifference> processedDifferences = inventoryDifferenceRepository
                    .findAll()
                    .stream()
                    .filter(d -> InventoryDifferenceConstants.STATUS_PROCESSED.equals(d.getStatus()))
                    .filter(d -> d.getProcessTime() != null && 
                            d.getProcessTime().after(startDate) && 
                            d.getProcessTime().before(endDate))
                    .collect(Collectors.toList());
            
            statistics.setProcessedCount(processedDifferences.size());
            statistics.setProcessedTotalDifference(processedDifferences.stream()
                    .map(TMpdInventoryDifference::getDifferenceQuantity)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            
            // 计算差异率统计
            BigDecimal totalStock = pendingDifferences.stream()
                    .map(TMpdInventoryDifference::getMesCurrentStock)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalStock.compareTo(BigDecimal.ZERO) > 0) {
                /*BigDecimal differenceRate = statistics.getPendingTotalDifference()
                        .divide(totalStock, 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));*/
                //statistics.setAverageDifferenceRate(differenceRate);
            } else {
                statistics.setAverageDifferenceRate(BigDecimal.ZERO);
            }
            
            // 按部门统计
            Map<String, Long> departmentStats = pendingDifferences.stream()
                    .collect(Collectors.groupingBy(
                            d -> d.getDepartment() != null ? d.getDepartment() : "未知部门",
                            Collectors.counting()));
            statistics.setDepartmentStatistics(departmentStats);
            
            // 按物料类型统计（根据物料编码前缀）
            Map<String, Long> materialTypeStats = pendingDifferences.stream()
                    .collect(Collectors.groupingBy(
                            d -> getMaterialTypeFromCode(d.getMaterialCode()),
                            Collectors.counting()));
            statistics.setMaterialTypeStatistics(materialTypeStats);
            
            log.info("库存差异统计信息获取完成：待处理{}条，已处理{}条", 
                    statistics.getPendingCount(), statistics.getProcessedCount());
            
            return statistics;
            
        } catch (Exception e) {
            log.error("获取库存差异统计信息失败", e);
            throw new RuntimeException("获取库存差异统计信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<InventoryDifferenceDTO> exportDifferences(InventoryDifferenceQueryDTO queryDTO) {
        // 设置大的页面大小来获取所有数据
        queryDTO.setPage(1);
        queryDTO.setPageSize(10000);

        Page<InventoryDifferenceDTO> page = queryInventoryDifferences(queryDTO);
        return page.getContent();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchProcessDifferences(List<InventoryDifferenceProcessDTO> processDTOList, Integer userId) {
        int processedCount = 0;

        for (InventoryDifferenceProcessDTO processDTO : processDTOList) {
            try {
                Boolean result = processDifference(processDTO, userId);
                if (result) {
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("批量处理差异失败，差异ID：{}", processDTO.getDifferenceId(), e);
            }
        }

        log.info("批量处理差异完成，成功处理{}条记录", processedCount);
        return processedCount;
    }

    /**
     * 创建差异处理记录
     * @param info 物料仓库信息
     * @param statisticsStartDate 统计开始日期
     * @param mesInitialStock MES期初库存
     * @return 差异处理记录
     */
    private TMpdInventoryDifference createDifferenceRecord(InventoryDifferenceDTO info, Date statisticsStartDate, BigDecimal mesInitialStock) {
        TMpdInventoryDifference difference = new TMpdInventoryDifference();

        difference.setMaterialCode(info.getMaterialCode());
        difference.setMaterialName(info.getMaterialName());
        difference.setErpWarehouseCode(info.getErpWarehouseCode());
        difference.setErpWarehouseName(info.getErpWarehouseName());
        difference.setDepartment(InventoryDifferenceConstants.DEFAULT_DEPARTMENT);
        difference.setUnit(info.getUnit());
        difference.setStatisticsStartDate(statisticsStartDate);
        difference.setStatus(InventoryDifferenceConstants.STATUS_PENDING);

        // 获取MES当前库存
        // 需要计算所有中心系统同步的数据
        BigDecimal mesCurrentStock = inventoryDifferenceMapper.getMesCurrentStock(info.getMaterialCode(), info.getErpWarehouseCode());
        difference.setMesCurrentStock(mesCurrentStock != null ? mesCurrentStock : BigDecimal.ZERO);
        difference.setMesInitialStock(mesInitialStock);

        Date currentDate = new Date();

        // 获取地磅收货数量
        BigDecimal weighbridgeQuantity = inventoryDifferenceMapper.getWeighbridgeReceiptQuantity(
                info.getMaterialCode(), statisticsStartDate, currentDate);
        difference.setWeighbridgeReceiptQuantity(weighbridgeQuantity != null ? weighbridgeQuantity : BigDecimal.ZERO);

        // 获取固废提报数量
        BigDecimal solidWasteQuantity = inventoryDifferenceMapper.getSolidWasteQuantity(
                info.getMaterialCode(), statisticsStartDate, currentDate);
        difference.setSolidWasteQuantity(solidWasteQuantity != null ? solidWasteQuantity : BigDecimal.ZERO);

        // 获取发料数量
        //根据物料类型去查询发料数量
        BigDecimal issuedQuantity = BigDecimal.ZERO;
        if (info.getMaterialType().equals("稻壳")){
            issuedQuantity = inventoryDifferenceMapper.getRiceIssuedQuantity(
                    info.getMaterialCode(), statisticsStartDate, currentDate);
        } else if (info.getMaterialType().equals("高粱")) {
            issuedQuantity = inventoryDifferenceMapper.getSorghumIssuedQuantity(
                    info.getMaterialCode(), statisticsStartDate, currentDate);
        }
        difference.setIssuedQuantity(issuedQuantity != null ? issuedQuantity : BigDecimal.ZERO);

        // 设置SAP库存（默认为0，需要手动同步）
        difference.setSapStock(BigDecimal.ZERO);

        // 计算差异数量
        calculateDifferenceQuantity(difference);

        return difference;
    }

    /**
     * 计算差异数量
     * @param difference 差异处理记录
     */
    private void calculateDifferenceQuantity(TMpdInventoryDifference difference) {
        // 差异数量 = MES当前库存 - (MES期初库存 + 地磅收货数量 - 固废提报数量 - 发料数量)
        BigDecimal theoreticalStock = difference.getMesInitialStock()
                .add(difference.getWeighbridgeReceiptQuantity())
                .subtract(difference.getSolidWasteQuantity())
                .subtract(difference.getIssuedQuantity());

        BigDecimal differenceQuantity = difference.getMesCurrentStock().subtract(theoreticalStock);
        difference.setDifferenceQuantity(differenceQuantity);
    }

    /**
     * 获取待处理差异统计信息
     * @return 待处理差异统计信息
     */
    public Map<String, Object> getPendingDifferenceStatistics() {
        log.info("获取待处理差异统计信息");
        
        try {
            // 获取基本统计信息
            Map<String, Object> basicStats = inventoryDifferenceMapper.getPendingStatistics();
            
            // 获取按部门统计信息
            List<Map<String, Object>> departmentStats = inventoryDifferenceMapper.getPendingStatisticsByDepartment();
            
            // 获取按物料类型统计信息
            List<Map<String, Object>> materialTypeStats = inventoryDifferenceMapper.getPendingStatisticsByMaterialType();
            
            // 组装结果
            Map<String, Object> result = new HashMap<>(basicStats);
            result.put("departmentStatistics", departmentStats);
            result.put("materialTypeStatistics", materialTypeStats);
            
            log.info("待处理差异统计信息获取完成，总数：{}", basicStats.get("total_count"));
            return result;
            
        } catch (Exception e) {
            log.error("获取待处理差异统计信息失败", e);
            throw new RuntimeException("获取待处理差异统计信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取已处理差异统计信息
     * @param days 统计天数
     * @return 已处理差异统计信息
     */
    public Map<String, Object> getProcessedDifferenceStatistics(Integer days) {
        log.info("获取已处理差异统计信息，统计天数：{}", days);
        
        try {
            // 计算时间范围
            Calendar calendar = Calendar.getInstance();
            Date endDate = calendar.getTime();
            calendar.add(Calendar.DAY_OF_MONTH, -days);
            Date startDate = calendar.getTime();
            
            // 获取基本统计信息
            Map<String, Object> basicStats = inventoryDifferenceMapper.getProcessedStatistics(startDate, endDate);
            
            // 获取按处理人统计信息
            List<Map<String, Object>> processorStats = inventoryDifferenceMapper.getProcessedStatisticsByProcessor(startDate, endDate);
            
            // 获取按日期统计信息
            List<Map<String, Object>> dateStats = inventoryDifferenceMapper.getProcessedStatisticsByDate(startDate, endDate);
            
            // 组装结果
            Map<String, Object> result = new HashMap<>(basicStats);
            result.put("statisticsDays", days);
            result.put("startDate", startDate);
            result.put("endDate", endDate);
            result.put("processorStatistics", processorStats);
            result.put("dateStatistics", dateStats);
            
            log.info("已处理差异统计信息获取完成，统计天数：{}，总数：{}", days, basicStats.get("total_count"));
            return result;
            
        } catch (Exception e) {
            log.error("获取已处理差异统计信息失败，统计天数：{}", days, e);
            throw new RuntimeException("获取已处理差异统计信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取历史记录查询
     * @param queryDTO 查询条件
     * @return 历史记录分页结果
     */
    public Page<InventoryDifferenceDTO> queryHistoryDifferences(InventoryDifferenceQueryDTO queryDTO) {
        log.info("查询历史差异记录，查询条件：{}", queryDTO);
        
        // 确保只查询已处理的记录
        queryDTO.setStatus(InventoryDifferenceConstants.STATUS_PROCESSED);
        
        return queryInventoryDifferences(queryDTO);
    }

    @Override
    public List<MaterialQuaryDTO> getMaterialWarehouseInfo() {
        return inventoryDifferenceMapper.getMaterialWarehouseInfo();
    }

    /**
     * 根据物料编码获取物料类型
     * @param materialCode 物料编码
     * @return 物料类型
     */
    private String getMaterialTypeFromCode(String materialCode) {
        if (materialCode == null || materialCode.length() < 2) {
            return "其他";
        }
        
        String prefix = materialCode.substring(0, 2).toUpperCase();
        switch (prefix) {
            case "RM":
                return "原料";
            case "PM":
                return "包装材料";
            case "AM":
                return "辅助材料";
            case "SM":
                return "备件材料";
            default:
                return "其他";
        }
    }

    /**
     * 转换明细实体为DTO
     * @param detail 明细实体
     * @return 明细DTO
     */
    private InventoryDifferenceDetailDTO convertDetailToDTO(TMpdInventoryDifferenceDetail detail) {
        InventoryDifferenceDetailDTO dto = new InventoryDifferenceDetailDTO();
        BeanUtils.copyProperties(detail, dto);
        return dto;
    }
}
