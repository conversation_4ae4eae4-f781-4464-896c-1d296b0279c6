package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.storage.inventory.*;
import com.hvisions.common.dto.ExcelExportDto;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: Jcao
 * @time: 2022/4/24 16:35
 */
public interface InventoryService {

    Page<InventoryPageDTO> getInventoryPageList(InventoryPageQueryDTO inventoryPageQueryDTO);

    Map<Integer, BigDecimal> getSapInventory(Integer id);

    Integer generateInventory(InventoryGenerateDTO inventoryGenerateDTO);

    Integer startInventory(Integer id);

    Integer finishInventory(List<InventoryFinishDTO> inventoryFinishs, Integer id, String checkPeople);

    Integer deleteInventory(Integer id);

    ExcelExportDto exportData(Integer id);

    /**
     * 获取盘点任务详情
     * @param id
     * @return
     */
    InventoryQueryDTO getInventoryDetail(Integer id);

    /**
     * 获取SAP库存
     * @param id
     * @return
     */
    InventoryQueryDTO getSapInventoryList(Integer id);

    Integer finishInventory(InventoryQueryDTO inventoryQuery);
}
