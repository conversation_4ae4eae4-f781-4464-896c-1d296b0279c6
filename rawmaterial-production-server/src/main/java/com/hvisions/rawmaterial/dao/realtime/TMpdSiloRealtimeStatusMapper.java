package com.hvisions.rawmaterial.dao.realtime;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusDTO;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusQueryDTO;
import com.hvisions.rawmaterial.entity.silo.TMpdSiloRealtimeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 筒仓实时状态信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Mapper
public interface TMpdSiloRealtimeStatusMapper extends BaseMapper<TMpdSiloRealtimeDO> {

    /**
     * 分页查询筒仓实时状态信息列表
     *
     * @param queryDTO 查询条件
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> selectSiloRealtimeStatusList(@Param("query") SiloRealtimeStatusQueryDTO queryDTO);

    /**
     * 根据业务系统和任务号查询筒仓实时状态信息
     *
     * @param businessSystem 业务系统
     * @param taskNo 任务号
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> selectByBusinessSystemAndTaskNo(@Param("businessSystem") String businessSystem, 
                                                                @Param("taskNo") String taskNo);

    /**
     * 根据筒仓号查询最新的实时状态信息
     *
     * @param siloNo 筒仓号
     * @return 筒仓实时状态信息
     */
    SiloRealtimeStatusDTO selectLatestBySiloNo(@Param("siloNo") String siloNo);

    /**
     * 根据物料类型查询筒仓实时状态信息
     *
     * @param materialType 物料类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> selectByMaterialType(@Param("materialType") String materialType);

    /**
     * 根据业务系统和物料类型查询筒仓实时状态信息
     *
     * @param businessSystem 业务系统
     * @param materialType 物料类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> selectByBusinessSystemAndMaterialType(@Param("businessSystem") String businessSystem,
                                                                      @Param("materialType") String materialType);

    /**
     * 根据数据类型查询筒仓实时状态信息
     *
     * @param dataType 数据类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> selectByDataType(@Param("dataType") String dataType);

    /**
     * 批量插入筒仓实时状态信息
     *
     * @param statusList 筒仓实时状态信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TMpdSiloRealtimeDO> statusList);

    /**
     * 根据任务号删除筒仓实时状态信息
     *
     * @param taskNo 任务号
     * @return 影响行数
     */
    int deleteByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 根据筒仓号和时间范围删除历史数据
     *
     * @param siloNo 筒仓号
     * @param beforeTime 时间点（删除此时间之前的数据）
     * @return 影响行数
     */
    int deleteHistoryData(@Param("siloNo") String siloNo, @Param("beforeTime") String beforeTime);


    BigDecimal[] selectBySiloNos(@Param("siloIdCodes") String[] siloIdCodes);

    TMpdSiloRealtimeDO selectByMaterialCodeAndTaskNoAndSiloNo(
            @Param("materialCode") String materialCode,
            @Param("taskNo") String taskNo,
            @Param("siloNo") String siloNo);

    TMpdSiloRealtimeDO selectByMaterialCodeAndQuantityAndSiloNoAndTaskNo(
            @Param("materialCode") String materialCode,
            @Param("inventoryWeight") BigDecimal inventoryWeight,
            @Param("siloNo") String siloNo,
            @Param("taskNo") String taskNo);

    /**
     * 根据筒仓号列表查询最新数据
     *
     * @param siloNoList 筒仓号列表
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> getListLatestSiloRealtimeStatusBySiloNo(@Param("siloNoList") List<String> siloNoList);
}
