package com.hvisions.rawmaterial.controller.difference;

import com.hvisions.rawmaterial.dto.SapStockSyncDTO;
import com.hvisions.rawmaterial.dto.difference.*;
import com.hvisions.rawmaterial.dto.material.MaterialQuaryDTO;
import com.hvisions.rawmaterial.service.InventoryDifferenceService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 原辅料库存盘点差异处理控制器
 * 
 * 提供库存差异处理的完整功能，包括：
 * - 差异记录的查询和管理
 * - 差异处理和批量处理
 * - SAP库存同步
 * - 统计信息查询
 * - 数据导出功能
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/rawmaterial/material-difference")
@Api(tags = "原辅料差异处理", description = "原辅料差异处理相关接口")
@Validated
public class MaterialDifferenceController {

    @Autowired
    private InventoryDifferenceService inventoryDifferenceService;
    
    @Autowired
    private com.hvisions.rawmaterial.service.async.AsyncTaskService asyncTaskService;

    /**
     * 分页查询库存差异处理记录
     * 
     * @param queryDTO 查询条件，包含分页参数和筛选条件
     * @return 分页查询结果
     */
    @ApiOperation(value = "分页查询库存差异处理记录", notes = "支持多种筛选条件的分页查询")
    @PostMapping("/page")
    public Page<InventoryDifferenceDTO> queryInventoryDifferences(
            @ApiParam(value = "查询条件", required = true) 
            @Valid @RequestBody InventoryDifferenceQueryDTO queryDTO) {
        log.info("分页查询库存差异处理记录，查询条件：{}", queryDTO);
        Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryInventoryDifferences(queryDTO);
        log.info("查询成功，返回{}条记录", result.getTotalElements());
        return result;
    }

    /**
     * 根据ID查询差异处理记录详情
     * 
     * @param id 差异处理记录ID
     * @return 差异处理记录详情
     */
    @ApiOperation(value = "根据ID查询差异处理记录详情", notes = "获取指定ID的差异处理记录完整信息")
    @GetMapping("/{id}")
    public InventoryDifferenceDTO getInventoryDifferenceById(
            @ApiParam(value = "差异处理记录ID", required = true, example = "1") 
            @PathVariable @NotNull Integer id) {
        
        log.info("查询差异处理记录详情，ID：{}", id);

        InventoryDifferenceDTO result = inventoryDifferenceService.getInventoryDifferenceById(id);
        log.info("查询成功，记录ID：{}，物料编码：{}", id, result.getMaterialCode());
        return result;
    }

    /**
     * 查询待处理的差异记录
     * 
     * @return 待处理的差异记录列表
     */
    @ApiOperation(value = "查询待处理的差异记录", notes = "获取所有状态为待处理的差异记录")
    @GetMapping("/pending")
    public ResponseEntity<List<InventoryDifferenceDTO>> getPendingDifferences() {
        
        log.info("查询待处理的差异记录");
        
        try {
            List<InventoryDifferenceDTO> result = inventoryDifferenceService.getPendingDifferences();
            log.info("查询成功，返回{}条待处理记录", result.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询待处理差异记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 物料查询
     */
    @ApiOperation(value = "物料查询", notes = "获取所有物料信息")
    @GetMapping("/material")
    public List<MaterialQuaryDTO> getMaterials() {
        log.info("开始查询物料信息");
        List<MaterialQuaryDTO> result = inventoryDifferenceService.getMaterialWarehouseInfo();
        log.info("查询成功，返回{}条物料信息", result.size());
        return result;
    }


    /**
     * 生成差异处理记录
     * 
     * @return 生成结果信息
     */
    @ApiOperation(value = "生成差异处理记录", notes = "系统自动计算MES与SAP库存差异并生成处理记录")
    @PostMapping("/generate")
    public Integer generateDifferenceRecords(@RequestBody InventoryDifferenceDTO inventoryDifferenceDTO) {
        log.info("开始生成差异处理记录");
        Integer count = inventoryDifferenceService.generateDifferenceRecords(inventoryDifferenceDTO);
        return count;
    }

    /**
     * 处理库存差异
     * 
     * @param processDTO 差异处理请求
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @ApiOperation(value = "处理库存差异", notes = "处理指定的库存差异记录，更新状态并创建新的待处理记录")
    @PostMapping("/process")
    public ResponseEntity<Map<String, Object>> processDifference(
            @ApiParam(value = "差异处理请求", required = true) 
            @Valid @RequestBody InventoryDifferenceProcessDTO processDTO,
            HttpServletRequest request) {
        
        log.info("处理库存差异，请求：{}", processDTO);
        
        try {
            // 从请求中获取用户ID（实际项目中应该从认证信息中获取）
            Integer userId = getUserIdFromRequest(request);
            
            Boolean success = inventoryDifferenceService.processDifference(processDTO, userId);
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "差异处理成功" : "差异处理失败");
            
            log.info("差异处理完成，结果：{}", success);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("处理库存差异失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "差异处理失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 批量处理差异
     * 
     * @param batchProcessDTO 批量处理请求
     * @param request HTTP请求对象
     * @return 批量处理结果
     */
    @ApiOperation(value = "批量处理差异", notes = "批量处理多个差异记录")
    @PostMapping("/batch-process")
    public ResponseEntity<Map<String, Object>> batchProcessDifferences(
            @ApiParam(value = "批量处理请求", required = true) 
            @Valid @RequestBody InventoryDifferenceBatchProcessDTO batchProcessDTO,
            HttpServletRequest request) {
        
        log.info("批量处理差异，请求：{}", batchProcessDTO);
        
        try {
            // 从请求中获取用户ID
            Integer userId = getUserIdFromRequest(request);
            
            // 将批量处理DTO转换为处理DTO列表
            List<InventoryDifferenceProcessDTO> processList = convertBatchToProcessList(batchProcessDTO);
            
            Integer successCount = inventoryDifferenceService.batchProcessDifferences(processList, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "批量处理完成");
            result.put("successCount", successCount);
            result.put("totalCount", processList.size());
            
            log.info("批量处理完成，成功处理{}条记录", successCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量处理差异失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量处理失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 重新计算差异
     * 
     * @param id 差异记录ID
     * @return 重新计算后的差异记录
     */
    @ApiOperation(value = "重新计算差异", notes = "重新计算指定记录的差异数量")
    @PostMapping("/{id}/recalculate")
    public ResponseEntity<InventoryDifferenceDTO> recalculateDifference(
            @ApiParam(value = "差异记录ID", required = true, example = "1") 
            @PathVariable @NotNull @Min(1) Integer id) {
        
        log.info("重新计算差异，记录ID：{}", id);
        
        try {
            InventoryDifferenceDTO existingRecord = inventoryDifferenceService.getInventoryDifferenceById(id);
            if (existingRecord == null) {
                log.warn("差异记录不存在，ID：{}", id);
                return ResponseEntity.notFound().build();
            }
            
            InventoryDifferenceDTO result = inventoryDifferenceService.calculateDifference(existingRecord);
            log.info("重新计算差异成功，记录ID：{}，差异数量：{}", id, result.getDifferenceQuantity());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("重新计算差异失败，记录ID：{}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 同步SAP库存
     * 
     * @param syncDTOList SAP库存同步数据列表
     * @return 同步结果
     */
    @ApiOperation(value = "同步SAP库存", notes = "批量同步SAP库存数据")
    @PostMapping("/sync-sap-stock")
    public ResponseEntity<Map<String, Object>> syncSapStock(
            @ApiParam(value = "SAP库存同步数据列表", required = true) 
            @Valid @RequestBody @NotEmpty List<SapStockSyncDTO> syncDTOList) {
        
        log.info("同步SAP库存，数据条数：{}", syncDTOList.size());
        
        try {
            Integer successCount = inventoryDifferenceService.syncSapStock(syncDTOList);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "SAP库存同步完成");
            result.put("successCount", successCount);
            result.put("totalCount", syncDTOList.size());
            
            log.info("SAP库存同步完成，成功同步{}条记录", successCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("同步SAP库存失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "SAP库存同步失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 手动同步单个物料的SAP库存
     * 
     * @param materialCode 物料编码
     * @param erpWarehouseCode ERP仓库编码
     * @return 同步结果
     */
    @ApiOperation(value = "手动同步单个物料的SAP库存", notes = "手动同步指定物料和仓库的SAP库存")
    @PostMapping("/sync-single-sap-stock")
    public ResponseEntity<Map<String, Object>> syncSingleSapStock(
            @ApiParam(value = "物料编码", required = true, example = "MAT001") 
            @RequestParam @NotNull String materialCode,
            @ApiParam(value = "ERP仓库编码", required = true, example = "WH001") 
            @RequestParam @NotNull String erpWarehouseCode) {
        
        log.info("手动同步单个物料SAP库存，物料编码：{}，仓库编码：{}", materialCode, erpWarehouseCode);
        
        try {
            Boolean success = inventoryDifferenceService.syncSingleSapStock(materialCode, erpWarehouseCode);
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "SAP库存同步成功" : "SAP库存同步失败");
            result.put("materialCode", materialCode);
            result.put("erpWarehouseCode", erpWarehouseCode);
            
            log.info("单个物料SAP库存同步完成，结果：{}", success);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动同步单个物料SAP库存失败，物料编码：{}，仓库编码：{}", materialCode, erpWarehouseCode, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "SAP库存同步失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 获取差异处理统计信息
     * 
     * @return 统计信息
     */
    @ApiOperation(value = "获取差异处理统计信息", notes = "获取差异处理的综合统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<InventoryDifferenceStatisticsDTO> getDifferenceStatistics() {
        
        log.info("获取差异处理统计信息");
        
        try {
            InventoryDifferenceStatisticsDTO result = inventoryDifferenceService.getDifferenceStatistics();
            log.info("获取统计信息成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取差异处理统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取待处理差异统计信息
     * 
     * @return 待处理差异统计信息
     */
    @ApiOperation(value = "获取待处理差异统计信息", notes = "获取当前待处理差异的统计信息")
    @GetMapping("/statistics/pending")
    public ResponseEntity<Map<String, Object>> getPendingDifferenceStatistics() {
        
        log.info("获取待处理差异统计信息");
        
        try {
            Map<String, Object> result = inventoryDifferenceService.getPendingDifferenceStatistics();
            log.info("获取待处理差异统计信息成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取待处理差异统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取已处理差异统计信息
     * 
     * @param days 统计天数
     * @return 已处理差异统计信息
     */
    @ApiOperation(value = "获取已处理差异统计信息", notes = "获取指定天数内已处理差异的统计信息")
    @GetMapping("/statistics/processed")
    public ResponseEntity<Map<String, Object>> getProcessedDifferenceStatistics(
            @ApiParam(value = "统计天数", required = false, defaultValue = "7", example = "7") 
            @RequestParam(defaultValue = "7") @Min(1) Integer days) {
        
        log.info("获取已处理差异统计信息，统计天数：{}", days);
        
        try {
            Map<String, Object> result = inventoryDifferenceService.getProcessedDifferenceStatistics(days);
            log.info("获取已处理差异统计信息成功，统计天数：{}", days);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取已处理差异统计信息失败，统计天数：{}", days, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 查询历史差异记录
     * 
     * @param queryDTO 查询条件
     * @return 历史记录分页结果
     */
    @ApiOperation(value = "查询历史差异记录", notes = "分页查询历史差异处理记录")
    @PostMapping("/history")
    public ResponseEntity<Page<InventoryDifferenceDTO>> queryHistoryDifferences(
            @ApiParam(value = "查询条件", required = true) 
            @Valid @RequestBody InventoryDifferenceQueryDTO queryDTO) {
        
        log.info("查询历史差异记录，查询条件：{}", queryDTO);
        
        try {
            Page<InventoryDifferenceDTO> result = inventoryDifferenceService.queryHistoryDifferences(queryDTO);
            log.info("查询历史差异记录成功，返回{}条记录", result.getTotalElements());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询历史差异记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导出差异记录
     * 
     * @param queryDTO 查询条件
     * @return 导出文件信息
     */
    @ApiOperation(value = "导出差异记录", notes = "根据查询条件导出差异记录到Excel文件")
    @PostMapping("/export")
    public ResponseEntity<Map<String, Object>> exportDifferenceRecords(
            @ApiParam(value = "查询条件") 
            @Valid @RequestBody InventoryDifferenceQueryDTO queryDTO) {
        
        log.info("导出差异记录，查询条件：{}", queryDTO);
        
        try {
            List<InventoryDifferenceDTO> exportData = inventoryDifferenceService.exportDifferences(queryDTO);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "导出成功");
            result.put("exportCount", exportData.size());
            result.put("data", exportData);
            
            log.info("导出差异记录成功，导出{}条记录", exportData.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("导出差异记录失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "导出失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 删除差异处理记录
     * 
     * @param id 差异处理记录ID
     * @return 删除结果
     */
    @ApiOperation(value = "删除差异处理记录", notes = "删除指定的差异处理记录")
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteDifference(
            @ApiParam(value = "差异处理记录ID", required = true, example = "1") 
            @PathVariable @NotNull @Min(1) Integer id) {
        
        log.info("删除差异处理记录，ID：{}", id);
        
        try {
            Boolean success = inventoryDifferenceService.deleteDifference(id);
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "删除成功" : "删除失败，记录不存在");
            
            log.info("删除差异处理记录完成，ID：{}，结果：{}", id, success);
            return success ? ResponseEntity.ok(result) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("删除差异处理记录失败，ID：{}", id, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 将批量处理DTO转换为处理DTO列表
     * 
     * @param batchProcessDTO 批量处理DTO
     * @return 处理DTO列表
     */
    private List<InventoryDifferenceProcessDTO> convertBatchToProcessList(InventoryDifferenceBatchProcessDTO batchProcessDTO) {
        List<InventoryDifferenceProcessDTO> processList = new java.util.ArrayList<>();
        
        for (InventoryDifferenceBatchDetailDTO batchDetail : batchProcessDTO.getBatchDetails()) {
            InventoryDifferenceProcessDTO processDTO = new InventoryDifferenceProcessDTO();
            processDTO.setId(batchDetail.getDifferenceId());
            processDTO.setStatisticsEndDate(batchProcessDTO.getStatisticsEndDate());
            processDTO.setProcessRemark(batchDetail.getProcessRemark());
            processDTO.setDetailList(batchDetail.getDetailList());
            
            processList.add(processDTO);
        }
        
        return processList;
    }

    /**
     * 从请求中获取用户ID
     * 实际项目中应该从认证信息中获取
     * 
     * @param request HTTP请求对象
     * @return 用户ID
     */
    private Integer getUserIdFromRequest(HttpServletRequest request) {
        // 这里应该从认证信息中获取用户ID
        // 暂时返回默认值
        String userIdHeader = request.getHeader("User-Id");
        if (userIdHeader != null) {
            try {
                return Integer.parseInt(userIdHeader);
            } catch (NumberFormatException e) {
                log.warn("无效的用户ID格式：{}", userIdHeader);
            }
        }
        return 1; // 默认用户ID
    }

    // ==================== 异步处理接口 ====================

    /**
     * 提交SAP库存同步异步任务
     * 
     * @param syncMessage SAP同步任务消息
     * @param request HTTP请求对象
     * @return 任务提交结果
     */
    @ApiOperation(value = "提交SAP库存同步异步任务", notes = "提交SAP库存同步任务到消息队列进行异步处理")
    @PostMapping("/async/sap-sync")
    public ResponseEntity<Map<String, Object>> submitSapSyncTask(
            @ApiParam(value = "SAP同步任务消息", required = true) 
            @Valid @RequestBody com.hvisions.rawmaterial.dto.async.SapSyncTaskMessage syncMessage,
            HttpServletRequest request) {
        
        log.info("提交SAP库存同步异步任务：{}", syncMessage.getTaskId());
        
        try {
            // 设置创建人信息
            Integer userId = getUserIdFromRequest(request);
            syncMessage.setCreatorId(userId);
            syncMessage.setCreatorName("用户" + userId); // 实际应该从用户服务获取用户名
            
            String taskId = asyncTaskService.submitSapSyncTask(syncMessage);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "SAP同步任务已提交");
            result.put("taskId", taskId);
            result.put("syncType", syncMessage.getSyncType());
            result.put("syncScope", syncMessage.getSyncScope());
            
            log.info("SAP库存同步异步任务提交成功，任务ID：{}", taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("提交SAP库存同步异步任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "任务提交失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 提交批量生成差异记录异步任务
     * 
     * @param generateMessage 批量生成任务消息
     * @param request HTTP请求对象
     * @return 任务提交结果
     */
    @ApiOperation(value = "提交批量生成差异记录异步任务", notes = "提交批量生成差异记录任务到消息队列进行异步处理")
    @ApiResponses({
        @ApiResponse(code = 200, message = "任务提交成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/async/batch-generate")
    public ResponseEntity<Map<String, Object>> submitBatchGenerateTask(
            @ApiParam(value = "批量生成任务消息", required = true) 
            @Valid @RequestBody com.hvisions.rawmaterial.dto.async.BatchGenerateTaskMessage generateMessage,
            HttpServletRequest request) {
        
        log.info("提交批量生成差异记录异步任务：{}", generateMessage.getTaskId());
        
        try {
            // 设置创建人信息
            Integer userId = getUserIdFromRequest(request);
            generateMessage.setCreatorId(userId);
            generateMessage.setCreatorName("用户" + userId); // 实际应该从用户服务获取用户名
            
            String taskId = asyncTaskService.submitBatchGenerateTask(generateMessage);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "批量生成任务已提交");
            result.put("taskId", taskId);
            result.put("processScope", generateMessage.getProcessScope());
            result.put("estimatedCount", generateMessage.getEstimatedCount());
            
            log.info("批量生成差异记录异步任务提交成功，任务ID：{}", taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("提交批量生成差异记录异步任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "任务提交失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 查询异步任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @ApiOperation(value = "查询异步任务状态", notes = "根据任务ID查询异步任务的执行状态")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 404, message = "任务不存在"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/async/task-status/{taskId}")
    public ResponseEntity<com.hvisions.rawmaterial.dto.async.TaskStatusMessage> getTaskStatus(
            @ApiParam(value = "任务ID", required = true, example = "TASK_1234567890_1") 
            @PathVariable @NotNull String taskId) {
        
        log.info("查询异步任务状态，任务ID：{}", taskId);
        
        try {
            com.hvisions.rawmaterial.dto.async.TaskStatusMessage status = asyncTaskService.getTaskStatus(taskId);
            
            if (status == null || status.getStatus() == com.hvisions.rawmaterial.dto.async.TaskStatusMessage.TaskStatus.FAILED && 
                "任务不存在或已过期".equals(status.getStatusDescription())) {
                log.warn("任务不存在或已过期，任务ID：{}", taskId);
                return ResponseEntity.notFound().build();
            }
            
            log.info("查询异步任务状态成功，任务ID：{}，状态：{}", taskId, status.getStatus());
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("查询异步任务状态失败，任务ID：{}", taskId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 查询用户的异步任务列表
     * 
     * @param taskType 任务类型（可选）
     * @param request HTTP请求对象
     * @return 用户任务列表
     */
    @ApiOperation(value = "查询用户的异步任务列表", notes = "查询当前用户的异步任务列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/async/user-tasks")
    public ResponseEntity<List<com.hvisions.rawmaterial.dto.async.TaskStatusMessage>> getUserTasks(
            @ApiParam(value = "任务类型", required = false, example = "SAP_SYNC") 
            @RequestParam(required = false) String taskType,
            HttpServletRequest request) {
        
        Integer userId = getUserIdFromRequest(request);
        log.info("查询用户异步任务列表，用户ID：{}，任务类型：{}", userId, taskType);
        
        try {
            List<com.hvisions.rawmaterial.dto.async.TaskStatusMessage> tasks = 
                asyncTaskService.getUserTasks(userId, taskType);
            
            log.info("查询用户异步任务列表成功，用户ID：{}，任务数量：{}", userId, tasks.size());
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("查询用户异步任务列表失败，用户ID：{}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 取消异步任务
     * 
     * @param taskId 任务ID
     * @return 取消结果
     */
    @ApiOperation(value = "取消异步任务", notes = "取消指定的异步任务")
    @ApiResponses({
        @ApiResponse(code = 200, message = "取消成功"),
        @ApiResponse(code = 404, message = "任务不存在"),
        @ApiResponse(code = 400, message = "任务无法取消"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/async/cancel-task/{taskId}")
    public ResponseEntity<Map<String, Object>> cancelTask(
            @ApiParam(value = "任务ID", required = true, example = "TASK_1234567890_1") 
            @PathVariable @NotNull String taskId) {
        
        log.info("取消异步任务，任务ID：{}", taskId);
        
        try {
            boolean success = asyncTaskService.cancelTask(taskId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "任务已取消" : "任务无法取消或不存在");
            result.put("taskId", taskId);
            
            log.info("取消异步任务完成，任务ID：{}，结果：{}", taskId, success);
            return success ? ResponseEntity.ok(result) : ResponseEntity.badRequest().body(result);
        } catch (Exception e) {
            log.error("取消异步任务失败，任务ID：{}", taskId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "取消任务失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 获取差异处理详情
     *
     * @param id 差异记录ID
     * @return 差异处理详情
     */
    @ApiOperation(value = "获取差异处理详情", notes = "获取指定差异记录的详细处理信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "记录不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{id}/details")
    public List<InventoryDifferenceDetailDTO> getInventoryDifferenceDetails(
            @ApiParam(value = "差异记录ID", required = true, example = "1")
            @PathVariable @NotNull @Min(1) Integer id) {

        log.info("获取差异处理详情，ID：{}", id);

        List<InventoryDifferenceDetailDTO> result = inventoryDifferenceService.getInventoryDifferenceDetails(id);
        return result;
    }
}
