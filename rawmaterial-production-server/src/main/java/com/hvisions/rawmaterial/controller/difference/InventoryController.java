package com.hvisions.rawmaterial.controller.difference;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.rawmaterial.dto.storage.Rl.MaterialSiloConfigDTO;
import com.hvisions.rawmaterial.dto.storage.inventory.*;
import com.hvisions.rawmaterial.service.InventoryService;
import com.hvisions.rawmaterial.service.materialsilo.MaterialSiloConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 盘点任务
 * @author: Jcao
 * @time: 2022/4/24 18:11
 */
@RestController
@RequestMapping(value = "/inventory")
@Api(tags = "盘点任务")
public class InventoryController {

    @Resource
    private InventoryService inventoryService;

    @Resource
    private MaterialSiloConfigService materialSiloConfigService;

    @ApiOperation(value = "分页查询盘点任务列表")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<InventoryPageDTO> getInventoryPageList(@RequestBody InventoryPageQueryDTO inventoryPageQueryDTO) {
        return inventoryService.getInventoryPageList(inventoryPageQueryDTO);
    }

    @ApiOperation(value = "查询SAP物料库存")
    @RequestMapping(value = "/sap/inventory/get/{id}", method = RequestMethod.GET)
    public Map<Integer, BigDecimal> getSapInventory(@PathVariable Integer id) {
        return inventoryService.getSapInventory(id);
    }

    @ApiOperation("库位查询-第一级")
    @RequestMapping(value = "/storage/get", method = RequestMethod.GET)
    public List<MaterialSiloConfigDTO> getSiloConfigFistList() {
        return materialSiloConfigService.getSiloConfigFistList();
    }


    @ApiOperation(value = "查询SAP物料库存-新")
    @RequestMapping(value = "/sap/inventory/getList/{id}", method = RequestMethod.GET)
    public InventoryQueryDTO getSapInventoryList(@PathVariable Integer id) {
        return inventoryService.getSapInventoryList(id);
    }

    @ApiOperation(value = "生成盘点任务")
    @RequestMapping(value = "/generate", method = RequestMethod.POST)
    public Integer generateInventory(@RequestBody InventoryGenerateDTO inventoryGenerateDTO) {
        return inventoryService.generateInventory(inventoryGenerateDTO);
    }

    @ApiOperation(value = "开始盘点任务")
    @RequestMapping(value = "/start/{id}", method = RequestMethod.GET)
    public Integer startInventory(@PathVariable Integer id) {
        return inventoryService.startInventory(id);
    }


    @ApiOperation(value = "完成盘点任务")
    @RequestMapping(value = "/finish/{id}/{checkPeople}", method = RequestMethod.POST)
    public Integer startInventory(@PathVariable Integer id, @PathVariable String checkPeople, @RequestBody List<InventoryFinishDTO> inventoryFinishs) {
        return inventoryService.finishInventory(inventoryFinishs, id, checkPeople);
    }

    @ApiOperation(value = "完成盘点任务-新")
    @RequestMapping(value = "/finish/{id}", method = RequestMethod.POST)
    public Integer startInventory(@RequestBody InventoryQueryDTO inventoryQuery) {
        return inventoryService.finishInventory(inventoryQuery);
    }

    /**
     * 实盘详情
     * @param id
     * @return
     */
    @ApiOperation(value = "实盘详情")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    public InventoryQueryDTO getInventoryDetail(@PathVariable Integer id) {
        return inventoryService.getInventoryDetail(id);
    }

    @ApiOperation(value = "删除盘点任务")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    public Integer deleteInventory(@PathVariable Integer id) {
        return inventoryService.deleteInventory(id);
    }

    @ApiOperation(value = "盘点任务明细导出")
    @RequestMapping(value = "/export/{id}", method = RequestMethod.GET)
    public ExcelExportDto exportData(@PathVariable Integer id) {
        return inventoryService.exportData(id);
    }
}
