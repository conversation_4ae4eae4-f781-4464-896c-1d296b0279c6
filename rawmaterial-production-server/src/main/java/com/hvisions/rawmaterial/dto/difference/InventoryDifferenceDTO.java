package com.hvisions.rawmaterial.dto.difference;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理DTO
 */
@Data
@ApiModel(value = "库存差异处理信息")
public class InventoryDifferenceDTO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "物料编码", required = true, example = "MAT001", notes = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称", required = true, example = "原料A", notes = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "ERP仓库编码", required = true, example = "WH001", notes = "ERP仓库编码")
    private String erpWarehouseCode;

    @ApiModelProperty(value = "ERP仓库名称", required = true, example = "原料仓库", notes = "ERP仓库名称")
    private String erpWarehouseName;

    @ApiModelProperty(value = "所属部门", example = "原辅料管理部", notes = "所属部门，长度不超过100个字符")
    private String department;

    @ApiModelProperty(value = "MES当前库存", example = "1000.50", notes = "MES当前库存数量")
    private BigDecimal mesCurrentStock;

    @ApiModelProperty(value = "MES期初库存", required = true, example = "800.00", notes = "MES期初库存数量")
    private BigDecimal mesInitialStock;

    @ApiModelProperty(value = "地磅收货数量", example = "500.00", notes = "地磅收货数量")
    private BigDecimal weighbridgeReceiptQuantity;

    @ApiModelProperty(value = "固废提报数量", example = "50.00", notes = "固废提报数量")
    private BigDecimal solidWasteQuantity;

    @ApiModelProperty(value = "发料数量", example = "300.00", notes = "发料数量")
    private BigDecimal issuedQuantity;

    @ApiModelProperty(value = "计量单位", required = true, example = "kg", notes = "计量单位")
    private String unit;

    @ApiModelProperty(value = "SAP库存", example = "950.00", notes = "SAP库存数量")
    private BigDecimal sapStock;

    @ApiModelProperty(value = "差异数量", example = "50.50", notes = "差异数量，系统自动计算")
    private BigDecimal differenceQuantity;

    @ApiModelProperty(value = "统计开始日期", required = true, example = "2024-01-01 00:00:00", notes = "统计开始日期")
    private Date statisticsStartDate;

    @ApiModelProperty(value = "统计结束日期", example = "2024-01-31 23:59:59", notes = "统计结束日期，不能早于统计开始日期")
    private Date statisticsEndDate;

    @ApiModelProperty(value = "状态", example = "1", notes = "状态：1-待处理，2-已处理", allowableValues = "1,2")
    private Integer status;

    @ApiModelProperty(value = "处理人ID", example = "1001", notes = "处理人ID")
    private Integer processorId;

    @ApiModelProperty(value = "处理人姓名", example = "张三", notes = "处理人姓名，长度不超过50个字符")
    private String processorName;

    @ApiModelProperty(value = "处理时间", example = "2024-01-15 10:30:00", notes = "处理时间")
    private Date processTime;

    @ApiModelProperty(value = "处理备注", example = "盘点差异处理", notes = "处理备注，长度不超过500个字符")
    private String processRemark;

    @ApiModelProperty(value = "SAP同步时间", example = "2024-01-15 11:00:00", notes = "SAP同步时间")
    private Date sapSyncTime;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 09:00:00", notes = "创建时间，系统自动生成")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-01-15 10:30:00", notes = "更新时间，系统自动生成")
    private Date updateTime;

    @ApiModelProperty(value = "物料类型", example = "高粱｜稻壳", notes = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "差异处理明细列表", notes = "差异处理明细列表")
    @Valid
    private List<InventoryDifferenceDetailDTO> detailList;
}
