package com.hvisions.rawmaterial.entity.difference;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存差异处理明细表
 * <AUTHOR>
 * @version 1.0
 * @Description: 库存差异处理明细实体类
 * @Date: 2024/07/14
 */
@Table(name = "t_mpd_inventory_difference_detail")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_inventory_difference_detail", comment = "库存差异处理明细表")
@Data
@TableName("t_mpd_inventory_difference_detail")
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
public class TMpdInventoryDifferenceDetail extends SysBase {

    /**
     * 差异处理ID
     */
    @Column(columnDefinition = "int NOT NULL COMMENT '差异处理ID'")
    private Integer differenceId;

    /**
     * 物料编码
     */
    @Column(length = 50, columnDefinition = "varchar(50) NOT NULL COMMENT '物料编码'")
    private String materialCode;

    /**
     * 物料名称
     */
    @Column(length = 100, columnDefinition = "varchar(100) NOT NULL COMMENT '物料名称'")
    private String materialName;

    /**
     * 计量单位
     */
    @Column(length = 20, columnDefinition = "varchar(20) NOT NULL COMMENT '计量单位'")
    private String unit;

    /**
     * 发放量
     */
    @Column(columnDefinition = "decimal(15,3) NOT NULL DEFAULT 0 COMMENT '发放量'")
    private BigDecimal actualQuantity;

    /**
     * 分摊比例
     */
    @Column(columnDefinition = "decimal(15,3) NOT NULL DEFAULT 0 COMMENT '分摊比例'")
    private BigDecimal shareRatio;

    /**
     * 分摊量
     */
    @Column(columnDefinition = "decimal(15,3) NOT NULL DEFAULT 0 COMMENT '分摊量'")
    private BigDecimal shareQuantity;

    /**
     * 中心
     */
    @Column(length = 50, columnDefinition = "varchar(50) NOT NULL COMMENT '中心'")
    private String center;

    /**
     * 处理备注
     */
    @Column(length = 500, columnDefinition = "varchar(500) COMMENT '处理备注'")
    private String processRemark;

    /**
     * 处理时间
     */
    @Column(columnDefinition = "datetime NOT NULL COMMENT '处理时间'")
    private Date processTime;
}
