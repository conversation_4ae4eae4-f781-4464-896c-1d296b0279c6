<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.InventoryMapper">

    <!-- 分页查询盘点任务列表 -->
    <!--<select id="getInventoryPageList" resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryPageDTO">
        SELECT
            i.id,
            i.order_no,
            i.`name`,
            i.billing_date,
            i.state,
            i.create_time,
            i.check_people,
            i.check_time,
            u.user_name AS create_name,
            (
                SELECT w.`name` AS warehouse_name FROM t_mpd_inventory_detail d
                LEFT JOIN t_mpd_rl_management l ON d.storage_id = l.id
                LEFT JOIN t_mpd_rl_management a ON l.parent_id = a.id
                LEFT JOIN t_mpd_warehouse w ON a.warehouse_id = w.id
                WHERE d.deleted = 0 AND d.inventory_id = i.id
                GROUP BY w.`name` LIMIT 1
            ) AS warehouse_name
        FROM
            t_mpd_inventory i
            LEFT JOIN authority.sys_user u ON i.creator_id = u.id
        WHERE i.deleted = 0
        <if test="orderNo != null and orderNo != ''">
            AND i.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="state != null and state != ''">
            AND i.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(i.create_time, '%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY i.id DESC
    </select>-->
    <select id="getInventoryPageList" resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryPageDTO">
        SELECT
        i.id,
        i.order_no,
        i.`name`,
        i.billing_date,
        i.state,
        i.create_time,
        i.check_people,
        i.check_time,
        u.user_name AS create_name,
        i.warehouse_name
        FROM
        t_mpd_inventory i
        LEFT JOIN authority.sys_user u ON i.creator_id = u.id
        WHERE i.deleted = 0
        <if test="orderNo != null and orderNo != ''">
            AND i.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="state != null and state != ''">
            AND i.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            AND DATE_FORMAT(i.create_time, '%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY i.id DESC
    </select>


    <!-- 根据物料类型id，获取物料编码集合 -->
    <select id="getMaterialCodes" resultType="java.lang.String">
        SELECT material_code FROM materials.hv_bm_material WHERE material_type = #{materialTypeId}
    </select>

    <!-- 根据盘点id获取所有的物料类型 -->
    <select id="getMaterialTypes"
            resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryMaterialTypeDTO" >
        SELECT
            mt.id,
            mt.material_type_code,
            mt.material_type_name,
            SUM(stock_quantity) AS stock_quantity
        FROM
            t_mpd_inventory_detail i
            LEFT JOIN t_mpd_rl_management l ON i.storage_id = l.id
            LEFT JOIN t_mpd_rl_management a ON l.parent_id = a.id
            LEFT JOIN t_mpd_warehouse w ON a.warehouse_id = w.id
            INNER JOIN materials.hv_bm_material m ON i.material_id = m.id
            INNER JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
        WHERE
            i.deleted = 0
          AND i.inventory_id = #{inventoryId}
        GROUP BY mt.material_type_code, mt.material_type_name
    </select>

    <!-- 获取库区信息 -->
    <select id="getInventoryAreaInfo"
            resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryAreaDTO">
        SELECT
            a.id AS area_id,
            a.`unit` AS area_unit,
            a.central_data,
            a.safety_stock,
            a.volume,
            a.`code` AS area_code,
            a.`name` AS area_name,
            m.material_type
        FROM
            t_mpd_inventory_detail i
            INNER JOIN t_mpd_rl_management l ON i.storage_id = l.id AND l.deleted = 0
            INNER JOIN t_mpd_rl_management a ON l.parent_id = a.id AND a.deleted = 0
            LEFT JOIN materials.hv_bm_material m ON i.material_id = m.id
        WHERE
            i.deleted = 0
          AND i.inventory_id = #{inventoryId}
        GROUP BY a.id, m.material_type
    </select>

    <!-- 获取盘点任务明细 -->
    <select id="getInventoryDetail"
            resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryDetailDTO">
        SELECT
            i.id,
            i.storage_id,
            l.id AS location_id,
            l.`code` AS location_code,
            l.`name` AS location_name,
            a.id AS area_id,
            a.`name` AS area_name,
            w.`name` AS warehouse_name,
            w.`code` AS warehouse_code,
            i.unit,
            i.material_id,
            i.material_code,
            i.material_name,
            i.stock_quantity,
            i.inventory_quantity,
            i.difference_quantity,
            m.material_type
        FROM
            t_mpd_inventory_detail i
            LEFT JOIN t_mpd_rl_management l ON i.storage_id = l.id
            LEFT JOIN t_mpd_rl_management a ON l.parent_id = a.id
            LEFT JOIN t_mpd_warehouse w ON a.warehouse_id = w.id
            LEFT JOIN materials.hv_bm_material m ON i.material_id = m.id
        WHERE
            i.deleted = 0
          AND i.inventory_id = #{id}
    </select>


    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.storage.inventory.InventoryQueryDTO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="billing_date" jdbcType="TIMESTAMP" property="billingDate" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="create_name" jdbcType="VARCHAR" property="createName" />
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
        <result column="check_people" jdbcType="VARCHAR" property="checkPeople" />
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
        <result column="state" jdbcType="VARCHAR" property="state" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>

    <select id="getInventoryAndDetailById" resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryQueryDTO">
        select i.id AS id,
        i.order_no AS order_no,
        i.billing_date AS billing_date,
        i.name AS name,
        u.user_name AS create_name,
        i.warehouse_name AS warehouse_name,
        i.check_people AS check_people,
        i.check_time AS check_time,
        i.state AS state,
        i.create_time AS create_time,
        i.update_time AS update_time
        from t_mpd_inventory i
        LEFT JOIN authority.sys_user u ON i.creator_id = u.id
        WHERE i.deleted = 0
        AND i.id = #{id}
        ORDER BY i.create_time DESC,i.id DESC
    </select>

    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.storage.inventory.InventoryDetailQueryDTO">
        SELECT ide.* FROM t_mpd_inventory_detail  ide
        WHERE inventory_id = #{id}
    </select>
</mapper>
