package com.hvisions.purchase.dto.purchase.code.maintenance;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description: 公司工厂代码新增修改dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "公司工厂代码新增修改dto")
public class CodeMaintenanceDTO extends SysBaseDTO {

    @ApiModelProperty(value = "公司编码")
    @NotNull(message = "公司编码不能为空")
    private String companyCode;

    @ApiModelProperty(value = "公司名称")
    @NotNull(message = "公司名称不能为空")
    private String companyName;

    @ApiModelProperty(value = "工厂编码")
    @NotNull(message = "工厂编码不能为空")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    @NotNull(message = "工厂名称不能为空")
    private String factoryName;

}
