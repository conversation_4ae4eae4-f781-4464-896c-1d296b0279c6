package com.hvisions.purchase.dto.purchase.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "采购计划进度数据")
public class ProcurementProgressDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    private Date endTime;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal procurementNumber;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "收货净重")
    private BigDecimal netWeightQty;
}
