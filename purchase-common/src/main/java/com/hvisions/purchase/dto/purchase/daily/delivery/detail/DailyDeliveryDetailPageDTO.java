package com.hvisions.purchase.dto.purchase.daily.delivery.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 日送货计划详情dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "日送货计划详情dto")
public class DailyDeliveryDetailPageDTO {

    @ApiModelProperty(value = "送货计划详情id")
    private Integer id;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "车辆状态： 0-已入场、1-质检中、2-可卸货、3-已卸货、4-异常待处理、5-拒收、6-出门、7-已收货、8-特殊退货")
    private String state;

    @ApiModelProperty(value = "同步状态： 0-待同步，1-已同步，2-同步失败")
    private String syncState;

    @ApiModelProperty(value = "质检状态： 0- 待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;

    @ApiModelProperty(value = "扣重是否处理；0-否，1-是")
    private String buckleIsHandle;

    @ApiModelProperty(value = "过账凭证号")
    private String matDoc;

    @ApiModelProperty(value = "入库筒仓id")
    private Integer warehouseId;

    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    @ApiModelProperty(value = "检验结果：合格/不合格")
    private String inspectResult;

    @ApiModelProperty(value = "不合格原因")
    private String inspectFailReason;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片地址")
    private String picUrls;

    @ApiModelProperty(value = "检验人--提交人")
    private String submitUser;
}
